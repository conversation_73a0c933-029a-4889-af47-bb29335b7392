import { useState } from 'react';
import { Menu } from 'lucide-react';
import { SideBar } from './components/SideBar/SideBar';
import { Header } from './components/Chat/Header';
import { ChatSection } from './components/Chat/ChatSection';
import { FooterChat } from './components/Chat/FooterChat';
import { AuthModal } from './components/Modals/AuthModal';
import { useAuth } from './hooks/useAuth';
import { useChatContext } from './context/ChatContext';
import { ToastContainer } from 'react-toastify';

function App() {
  const { user } = useAuth();
  const [emailStyle, setEmailStyle] = useState("formal");
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  // Usar el contexto para obtener el estado y las funciones del chat
  const { messages, sendChatMessage, handleSendEmail, loading, sendingEmail } = useChatContext();

  // La función de envío de mensajes ahora verifica autenticación
  const handleSendMessage = (prompt: string) => {
    if (!user) {
      setIsAuthModalOpen(true);
      return;
    }
    sendChatMessage(prompt, emailStyle);
  };

  return (
    <main className="h-screen flex gap-1 bg-bg">
      {/* Sidebar con panel deslizable para móviles */}
      <div className={`fixed inset-y-0 left-0 z-30 w-4/5 max-w-sm bg-bg transform transition-transform duration-300 ease-in-out ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:relative lg:translate-x-0 lg:inset-auto lg:w-1/6`}>
        <SideBar />
      </div>

      {/* Overlay para móviles */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <section className="h-full w-full p-2 lg:p-5">
        <div className="h-full w-full grid grid-rows-[auto_auto_1fr_auto] lg:grid-rows-[auto_1fr_auto] bg-white rounded-4xl p-3 lg:p-7 shadow-lg overflow-hidden">
          {/* Cabecera específica para móviles */}
          <div className="flex items-center justify-between p-2 lg:hidden">
            <button onClick={() => setSidebarOpen(true)} className="p-2">
              <Menu className="w-6 h-6" />
            </button>
            <Header onStyleChange={setEmailStyle} />
          </div>

          {/* Selector de estilo para móviles - debajo de la cabecera */}
          <div className="flex justify-center p-2 lg:hidden">
            <select
              value={emailStyle}
              onChange={(e) => setEmailStyle(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm"
            >
              <option value="formal">Formal</option>
              <option value="direct">Direct</option>
              <option value="informal">Informal</option>
              <option value="funny">Funny</option>
            </select>
          </div>

          {/* Header original - solo visible en pantallas grandes */}
          <div className="hidden lg:flex">
            <Header onStyleChange={setEmailStyle} />
          </div>

          <div className="overflow-y-auto min-h-0 flex-1 px-2 lg:px-0">
            <ChatSection
              messages={messages}
              onSendEmail={(draftContent) => handleSendEmail(draftContent, user?.email || '')}
              loading={loading}
              sendingEmail={sendingEmail}
            />
          </div>

          <div className="px-2 lg:px-0">
            <FooterChat
              sendChatMessage={handleSendMessage}
              userId={user?.id || ''}
              isDisabled={sendingEmail}
            />
          </div>
        </div>
      </section>

      {/* Modal de autenticación */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={() => setIsAuthModalOpen(false)} 
      />

      <ToastContainer />
    </main>
  );
}

export default App;
