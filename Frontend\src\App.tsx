import { useState } from 'react';
import { Menu } from 'lucide-react';
import { SideBar } from './components/SideBar/SideBar';
import { Header } from './components/Chat/Header';
import { ChatSection } from './components/Chat/ChatSection';
import { FooterChat } from './components/Chat/FooterChat';
import { AuthModal } from './components/Modals/AuthModal';
import { useAuth } from './hooks/useAuth';
import { useChatContext } from './context/ChatContext';
import { ToastContainer } from 'react-toastify';

function App() {
  const { user } = useAuth();
  const [emailStyle, setEmailStyle] = useState("formal");
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  // Usar el contexto para obtener el estado y las funciones del chat
  const { messages, sendChatMessage, handleSendEmail, loading, sendingEmail } = useChatContext();

  // La función de envío de mensajes ahora verifica autenticación
  const handleSendMessage = (prompt: string) => {
    if (!user) {
      setIsAuthModalOpen(true);
      return;
    }
    sendChatMessage(prompt, emailStyle);
  };

  return (
    <main className="h-screen flex gap-1 bg-bg">
      {/* Sidebar con panel deslizable para móviles */}
      <div className={`fixed inset-y-0 left-0 z-30 w-4/5 max-w-sm bg-bg transform transition-transform duration-300 ease-in-out ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:relative lg:translate-x-0 lg:inset-auto lg:w-1/6`}>
        <SideBar />
      </div>

      {/* Overlay para móviles */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <section className="h-full w-full p-5">
        <div className="h-full w-full grid grid-rows-[auto_1fr_auto] bg-white rounded-4xl p-7 shadow-lg overflow-hidden">
          {/* Cabecera específica para móviles */}
          <div className="flex items-center justify-between p-4 bg-white rounded-t-4xl lg:hidden">
            <button onClick={() => setSidebarOpen(true)}>
              <Menu />
            </button>
            <Header onStyleChange={setEmailStyle} />
          </div>

          {/* Header original - solo visible en pantallas grandes */}
          <div className="hidden lg:flex">
            <Header onStyleChange={setEmailStyle} />
          </div>

          <div className="overflow-y-auto min-h-0 flex-1 ">
            <ChatSection
              messages={messages}
              onSendEmail={(draftContent) => handleSendEmail(draftContent, user?.email || '')}
              loading={loading}
              sendingEmail={sendingEmail}
            />
          </div>

          <FooterChat
            sendChatMessage={handleSendMessage}
            userId={user?.id || ''}
            isDisabled={sendingEmail}
          />
        </div>
      </section>

      {/* Modal de autenticación */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={() => setIsAuthModalOpen(false)} 
      />

      <ToastContainer />
    </main>
  );
}

export default App;
